# 页面爬取需求

### 🗡页面区域划分

![api-management-snapshot](D:\Playground\AzureCNArchaeologist\页面爬取需求\页面爬取需求\页面爬取需求.assets\FireShot-API 管理定价.png)

#### 💡说明

- 页面中❌部分不需要
- 描述信息位于banner下的第一个section，请尝试提取出来
- Banner，描述区域，Q&A区域是公共区域，html片段传递一次即可

### 🛎图片路径处理

图片路径在acn中大多是绝对路径，少数为相对路径。散落在标签的style中和img的src中

例如

```html
<div class="common-banner-image" style="background-image: url("/Images/marketing-resource/css/event-grid_banner.png");

<img src="/Images/marketing-resource/css/<EMAIL>" alt="imgAlt">
```

现在期望爬取数据时将图片的域名作为占位符填充上，这样我后端填充数据时，会填充上完整的路径名，以便于编辑和展示

例如

```html
<div class="common-banner-image" style="background-image: url("{img_hostname}/Images/marketing-resource/css/event-grid_banner.png");

<img src="{img_hostname}/Images/marketing-resource/css/<EMAIL>" alt="imgAlt">
```

此种做法有一定的难度，请尝试可行性。实在不可行再讨论其他方案

### ⚙️需要的参数

传入进来的json示例

```json
{
  "Title": "定价 - 事件网格 - Azure 云计算",
  "MetaDescription": "了解 Azure 事件网格定价，它是一款完全托管的“发布-订阅”事件处理程序。它基于操作数进行计费，每月前 100,000 个操作免费。",
  "MetaKeywords": "事件处理程序和事件处理, 发布订阅和发布订阅模型, 反应式编程和基于事件的编程",
  "Slug": "event-grid",
  "MsServiceName":"event-grid"
  "DescriptionContent":"", 
  "Language": "zh-CN",
  "NavigationTitle": "事件网格",
  "BannerContent": "",
  "QaContent": "",
  "NorthChinaContent": "",
  "NorthChina2Content": "",
  "NorthChina3Content": "",
  "EastChinaContent": "",
  "EastChina2Content": "",
  "EastChina3Content": "",
  "NoRegionContent": "",
  "HasRegion": false
}
```

#### 🔮参数说明

- Title：head中的title

- MetaDescription：head >meta中的description

  ```html
  <meta content="了解 Azure 事件网格定价，它是一款完全托管的“发布-订阅”事件处理程序。它基于操作数进行计费，每月前 100,000 个操作免费。" name="description">
  ```

- MetaKeywords：head>meta中的keywords

  ```
  <meta content="事件处理程序和事件处理, 发布订阅和发布订阅模型, 反应式编程和基于事件的编程" name="keywords">
  ```

- Slug：CMS中的页面唯一标识，尝试了多个页面，发现只有URL中获取最为保险，比如事件网格的url：https://www.azure.cn/pricing/details/event-grid/，其中的Slug应为event-grid

- MsServiceName：获取左侧菜单使用的标识，源自于tags标签中的ms.service属性的值

  ```html
  <tags ms.date="09/30/2015" ms.service="event-grid" wacn.date="11/27/2015"></tags>
  ```

- DescriptionContent：banner下描述内容的html

- Language：两个值，`en-US`和`zh-CN`表示是中文站还是英文站，默认为中文站

- NavigationTitle：CMS中显示用值，和Tile不同。取值源自于common-banner-title>h2

![image-20250714170132392](页面爬取需求.assets/image-20250714170132392.png)

- BannerContent：Banner部分的html片段
- QaContent：Q&A部分的html片段
- NorthChinaContent：中国北部html
- NorthChina2Content：中部北部2html
- NorthChina3Content：中国北部3html
- EastChinaContent：中国东部html
- EastChina2Content：中国东部2html
- NoRegionContent：当页面没有地区选项时，主体内容填充到这个字段中
- HasRegion：标注页面是否含有地区选项，如果为false这程序不会扫描其他地区数据，直接填充和渲染NoRegionContent内容❗️

💡字段中所有以Content结尾的，传入的都是html片段

