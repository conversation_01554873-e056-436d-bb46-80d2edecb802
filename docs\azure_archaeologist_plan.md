# AzureCNArchaeologist 项目实施方案文档

## 项目概述

### 项目背景
Azure中国定价网站 (https://www.azure.cn/pricing/) 原维护团队已解散，前端JavaScript代码丢失。项目团队已获得完整的HTML源码文件，需要通过"HTML解析式考古"，从大量HTML文件中提取结构化数据，重建整个产品价格和计算器页面系统。

### 项目目标
1. **智能解析**：从现有HTML文件中智能提取所有产品信息、价格数据、描述和图片路径
2. **深度建模**：构建结构化的数据模型，支持复杂定价计算逻辑和明细展示
3. **CMS就绪**：输出标准化数据格式，便于后续CMS系统导入和团队手动维护

### 项目范围
- **HTML解析模块**：批量HTML文件智能解析
- **数据提取模块**：结构化数据提取和清洗
- **定价重建模块**：复杂计算逻辑重构和明细化
- **导出模块**：多格式数据导出和CMS适配

## 技术架构设计

### 核心技术栈
- **编程语言**：Python 3.11+
- **Python 包管理**：poetry / uv
- **HTML解析**：BeautifulSoup + lxml + selectolax
- **数据处理**：pandas + numpy + jieba
- **传统RAG组件**：
  - **Embedding模型**：sentence-transformers / text-embedding-3-large / qwen3-embedding
  - **向量存储**：faiss / chromadb / qdrant / milvus / azure-vector-store?
  - **Rerank模型**：cross-encoder / cohere-rerank / qwen3-rerank
- **大上下文RAG**：transformers + deepseek-api + openai-api
- **数据存储**：Postgresql + MongoDB + SQLAlchemy + JSON
- **文档处理**：python-docx + PyPDF2 + markdown + Mineru
- **可视化**：matplotlib + plotly + graphviz
- **性能优化**：multiprocessing + asyncio + redis + caching

### 系统架构
```
AzureCNArchaeologist/
├── analysis/           # HTML分析模块
├── parsing/            # HTML解析模块
├── extraction/         # 数据提取模块
├── processing/         # 数据处理模块
├── modeling/           # 定价建模模块
├── rag_preparation/    # RAG数据准备模块
├── validation/         # 数据验证模块
├── export/             # 数据导出模块
├── utils/              # 工具类库
├── config/             # 配置文件
├── data/               # 数据目录
│   ├── html_source/    # 原始HTML文件
│   ├── parsed/         # 解析后数据
│   ├── processed/      # 处理后数据
│   ├── rag_ready/      # RAG就绪数据
│   ├── vectors/        # 向量化数据
│   ├── metadata/       # 元数据文件
│   ├── images/         # 图片资源路径
│   └── exports/        # 最终输出
└── docs/               # 文档目录
```

## 阶段一：HTML文件分析与分类

### 1.1 HTML文件清单与分类
**目标**：分析所有HTML文件，建立完整的内容清单和分类体系

**实施内容**：
- **文件扫描与编目**：扫描所有HTML文件，建立文件清单
- **页面类型自动识别**：基于HTML结构和内容自动分类页面类型
- **内容丰富度评估**：评估每个文件的数据价值和信息密度
- **服务映射建立**：建立文件名与Azure服务的对应关系
- **优先级排序**：根据商业价值对文件进行处理优先级排序

**技术要求**：
- 实现HTML文件的批量扫描和分析
- 开发基于内容特征的页面分类算法
- 建立文件与服务的智能映射机制
- 创建数据价值评估算法

**输出物**：
- `html_inventory.csv`：HTML文件完整清单
- `page_classification.json`：页面类型分类结果
- `processing_priority.json`：处理优先级队列

### 1.2 HTML结构模式识别
**目标**：识别HTML中的数据模式，为批量提取做准备

**实施内容**：
- **表格结构分析**：识别定价表格的各种HTML结构模式
- **内容块识别**：识别服务描述、特性列表、价格信息等内容块
- **样式类名分析**：分析CSS类名，发现数据提取的规律
- **模板模式识别**：识别相同模板生成的页面，建立通用解析规则
- **异常结构标记**：标记结构异常或格式特殊的HTML文件

**技术要求**：
- 开发HTML结构的模式识别算法
- 建立可复用的解析规则库
- 实现异常结构的自动检测
- 创建解析规则的可视化工具

**输出物**：
- `html_patterns.json`：HTML结构模式库
- `parsing_rules.json`：通用解析规则集
- `anomaly_files.csv`：异常结构文件清单

## 阶段二：HTML解析与数据提取

### 2.1 智能HTML解析引擎
**目标**：建立高效的HTML批量解析系统，从HTML中提取结构化数据

**实施内容**：
- **多策略解析器**：针对不同页面类型的专门解析策略
- **表格智能识别**：自动识别和解析各种复杂的定价表格
- **内容块提取器**：提取服务描述、功能特性、技术规格等内容
- **价格信息提取**：从各种格式的文本中准确提取价格数据
- **图片路径提取**：收集所有相关图片和图标的路径信息

**技术要求**：
- 使用BeautifulSoup和lxml进行高效HTML解析
- 实现多线程并行处理大量HTML文件
- 建立智能的内容识别和分类算法
- 实现全面的异常处理和错误恢复

**输出物**：
- 结构化数据JSON文件集合
- HTML解析日志和错误报告
- 解析统计和性能报告

### 2.2 定价表格专项解析
**目标**：专门处理复杂的Azure定价表格，确保定价数据的准确性

**实施内容**：
- **表格结构识别**：自动识别表头、数据行、合并单元格等结构
- **价格文本解析**：从复杂的价格文本中提取数值、单位、条件
- **计费周期标准化**：统一按小时、按月、按年等不同计费周期
- **区域价格区分**：区分不同地区的价格差异
- **特殊定价规则**：识别阶梯定价、批量折扣等特殊规则

**技术要求**：
- 开发专门的表格解析算法
- 实现价格文本的正则表达式匹配
- 建立价格数据的验证和校正机制
- 创建定价规则的形式化表示

**输出物**：
- `raw_pricing_tables.json`：原始定价表格数据
- `parsed_pricing.json`：解析后的结构化定价数据
- `pricing_patterns.json`：发现的定价模式库

### 2.3 服务信息综合提取
**目标**：全面提取每个Azure服务的详细信息

**实施内容**：
- **服务基本信息**：名称、描述、分类、图标、文档链接
- **技术规格提取**：性能指标、容量限制、技术参数
- **功能特性整理**：核心功能、附加功能、使用限制
- **使用场景描述**：适用场景、最佳实践、注意事项
- **相关服务关联**：依赖服务、互补服务、替代服务

**技术要求**：
- 实现智能的文本内容提取和清洗
- 建立服务信息的标准化数据模型
- 实现多页面信息的自动合并和去重
- 建立信息完整性和准确性的验证机制

**输出物**：
- `service_profiles.json`：完整的服务档案
- `technical_specs.json`：技术规格数据
- `feature_matrix.json`：功能特性矩阵

## 阶段三：数据清洗与标准化

### 3.1 数据质量控制
**目标**：确保抓取数据的准确性、完整性和一致性

**实施内容**：
- **数据完整性检查**：检测缺失字段和空值
- **数据一致性验证**：跨页面数据的一致性检查
- **格式标准化**：统一价格格式、日期格式、文本格式
- **重复数据识别**：检测和合并重复的服务或价格信息
- **异常数据标记**：标记异常的价格或描述数据

**技术要求**：
- 开发全面的数据验证规则引擎
- 实现智能的重复检测算法
- 建立数据质量评分体系

**输出物**：
- `data_quality_report.json`：数据质量报告
- `data_issues.csv`：数据问题清单
- `cleaning_log.json`：清洗操作日志

### 3.2 文本处理与语义理解
**目标**：对文本内容进行深度处理，提取语义信息

**实施内容**：
- **中文文本分词**：使用jieba进行准确的中文分词
- **关键词提取**：提取服务和功能的关键词
- **语义相似度计算**：计算服务间的语义相似度
- **自动摘要生成**：为长描述生成简洁摘要
- **技术特征识别**：识别技术规格和性能指标

**技术要求**：
- 整合多种NLP工具和算法
- 建立领域专用的词典和停用词表
- 实现语义向量化和相似度计算

**输出物**：
- `processed_texts.json`：处理后的文本数据
- `keywords_dictionary.json`：关键词词典
- `semantic_relationships.json`：语义关系网络

### 3.3 价格数据标准化
**目标**：统一价格数据格式，建立标准的定价数据结构

**实施内容**：
- **价格格式统一**：统一货币单位、计费周期、精度
- **计费单位标准化**：统一时间单位（小时/月）、容量单位（GB/TB）
- **区域价格整理**：整理不同区域的价格差异
- **历史价格版本**：建立价格变更的版本管理
- **价格有效性验证**：验证价格数据的合理性

**技术要求**：
- 实现智能的价格文本解析
- 建立价格数据的标准化流水线
- 实现价格合理性的自动检查

**输出物**：
- `standardized_pricing.json`：标准化价格数据
- `pricing_schema.json`：价格数据模式定义
- `price_validation_report.json`：价格验证报告

## 阶段四：数据建模与关系构建

### 4.1 服务分类体系构建
**目标**：建立科学的Azure服务分类体系

**实施内容**：
- **层级分类设计**：设计多级服务分类结构
- **自动分类算法**：基于服务名称和描述的自动分类
- **分类标准化**：统一分类标准和命名规范
- **分类关系映射**：建立分类间的父子和相关关系
- **分类标签体系**：为每个分类添加描述性标签

**技术要求**：
- 使用机器学习算法进行自动分类
- 建立分类的层次结构数据模型
- 实现分类的可视化展示

**输出物**：
- `service_taxonomy.json`：服务分类体系
- `classification_rules.json`：分类规则定义
- `category_relationships.json`：分类关系图

### 4.2 定价模型重构
**目标**：重构复杂的Azure定价计算逻辑

**实施内容**：
- **基础定价模型**：按需付费、包年包月等基础模型
- **阶梯定价引擎**：支持用量阶梯的复杂定价
- **折扣规则引擎**：批量折扣、长期承诺折扣等
- **组合定价模型**：多服务组合的定价计算
- **区域定价差异**：不同区域的价格差异模型

**技术要求**：
- 设计灵活的定价规则引擎
- 实现可扩展的计算框架
- 建立定价公式的形式化表示

**输出物**：
- `pricing_engine.py`：定价计算引擎
- `pricing_formulas.json`：定价公式库
- `discount_rules.json`：折扣规则集

### 4.3 服务关系网络
**目标**：构建服务间的复杂关系网络

**实施内容**：
- **依赖关系识别**：识别服务间的技术依赖
- **互补关系分析**：发现经常一起使用的服务组合
- **竞争关系标记**：标记功能重叠的竞争性服务
- **升级路径映射**：建立服务版本和升级路径
- **推荐算法基础**：为服务推荐建立数据基础

**技术要求**：
- 使用图算法分析服务关系
- 建立多维度的关系权重计算
- 实现关系网络的可视化

**输出物**：
- `service_relationships.json`：服务关系网络
- `dependency_graph.json`：依赖关系图
- `recommendation_matrix.json`：推荐关系矩阵

## 阶段五：计算器逻辑重建

### 5.1 计算引擎架构
**目标**：设计可扩展的定价计算引擎架构

**实施内容**：
- **计算器框架设计**：模块化的计算器架构
- **规则引擎实现**：支持复杂业务规则的引擎
- **参数验证器**：输入参数的验证和约束检查
- **计算结果格式化**：统一的计算结果输出格式
- **计算过程追踪**：详细的计算步骤记录

**技术要求**：
- 设计高度可配置的计算框架
- 实现计算过程的完整追踪
- 建立计算结果的缓存机制

**输出物**：
- `calculator_framework.py`：计算器框架
- `calculation_rules.json`：计算规则配置
- `validator_schemas.json`：参数验证模式

### 5.2 复杂定价逻辑实现
**目标**：实现Azure特有的复杂定价计算逻辑

**实施内容**：
- **阶梯计费算法**：实现用量阶梯的精确计算
- **时间相关计费**：处理按小时、按月的不同计费周期
- **资源组合计费**：多资源组合使用的计费逻辑
- **优惠策略计算**：各种折扣和优惠的叠加计算
- **税费和汇率处理**：税费计算和汇率转换

**技术要求**：
- 实现高精度的浮点数计算
- 处理复杂的时间计算逻辑
- 建立完整的测试用例覆盖

**输出物**：
- `pricing_algorithms.py`：核心定价算法
- `calculation_examples.json`：计算示例集
- `test_cases.json`：测试用例库

### 5.3 计算结果展示
**目标**：设计清晰的计算过程和结果展示

**实施内容**：
- **计算步骤分解**：将复杂计算分解为清晰步骤
- **费用明细展示**：详细的费用构成和明细
- **可视化图表**：费用分布和趋势的图表展示
- **对比分析功能**：不同配置方案的对比
- **报告生成功能**：可导出的计算报告

**技术要求**：
- 实现清晰的数据结构设计
- 建立标准的展示模板
- 支持多种输出格式

**输出物**：
- `display_templates.json`：展示模板
- `chart_configs.json`：图表配置
- `report_generators.py`：报告生成器

## 阶段六：混合智能RAG数据准备

### 6.1 多层级RAG架构设计
**目标**：设计兼顾效率和质量的混合RAG系统，平衡信息完整性与成本控制

**实施内容**：
- **metadata驱动过滤层**：通过丰富metadata实现精准的预过滤
- **智能embedding层**：基于产品/服务维度的有意义向量化
- **rerank优化层**：对候选结果进行语义重排序
- **上下文组装层**：智能组装完整上下文，避免信息碎片化
- **成本优化策略**：动态选择检索策略，平衡质量与成本

**技术要求**：
- 设计多层级的检索架构，支持不同复杂度的查询
- 实现智能的策略选择算法，根据查询类型选择最优方案
- 建立成本监控和优化机制
- 支持检索策略的动态调整

**输出物**：
- `hybrid_rag_architecture.json`：混合RAG架构设计
- `retrieval_strategies.json`：检索策略配置
- `cost_optimization.json`：成本优化策略

### 6.2 智能metadata索引体系
**目标**：构建高效的metadata过滤系统，实现精准的预检索

**实施内容**：
- **多维度metadata设计**：服务类型、价格区间、使用场景、技术特征等
- **层次化分类体系**：构建服务的多层级分类和标签系统
- **查询意图识别**：自动识别用户查询的意图和范围
- **智能过滤算法**：基于metadata的快速过滤和范围缩小
- **相关性预评估**：在embedding前进行相关性初步评估

**技术要求**：
- 建立结构化的metadata索引系统
- 实现高效的多维度查询和过滤
- 设计智能的查询意图识别算法
- 建立metadata的自动更新和维护机制

**输出物**：
- `metadata_index_system.json`：metadata索引系统
- `classification_hierarchy.json`：分类层次结构
- `intent_recognition_rules.json`：意图识别规则
- `filter_algorithms.py`：过滤算法实现

### 6.3 产品维度智能向量化
**目标**：实现有意义的向量化策略，避免盲目切片造成的信息损耗

**实施内容**：
- **产品级完整embedding**：以单个Azure服务为单位进行向量化
- **功能维度embedding**：针对服务的核心功能进行专门向量化
- **定价维度embedding**：对定价相关信息进行专门处理
- **场景维度embedding**：基于使用场景的内容向量化
- **关联关系embedding**：服务间依赖和推荐关系的向量化

**技术要求**：
- 使用先进的embedding模型（如text-embedding-3-large）
- 实现多维度的向量化策略
- 建立向量质量评估和优化机制
- 支持增量更新和版本管理

**输出物**：
- `product_embeddings.h5`：产品级向量数据
- `feature_embeddings.h5`：功能维度向量
- `pricing_embeddings.h5`：定价相关向量
- `relationship_embeddings.h5`：关联关系向量

### 6.4 混合检索策略引擎
**目标**：根据查询复杂度和精度要求，智能选择最优检索策略

**实施内容**：
- **快速检索模式**：简单查询使用metadata+embedding的轻量级检索
- **精准检索模式**：复杂查询使用多层级检索+rerank优化
- **完整上下文模式**：高精度需求使用大上下文窗口完整检索
- **成本控制策略**：根据查询重要性和用户等级选择策略
- **性能监控优化**：实时监控不同策略的效果和成本

**技术要求**：
- 实现多种检索策略的无缝切换
- 建立查询复杂度的自动评估
- 实现成本和质量的动态平衡
- 建立策略效果的持续优化机制

**输出物**：
- `retrieval_engine.py`：混合检索引擎
- `strategy_selector.py`：策略选择器
- `performance_monitor.py`：性能监控器
- `cost_tracker.json`：成本追踪配置

### 6.5 上下文智能组装
**目标**：将检索到的内容片段智能组装成完整、连贯的上下文

**实施内容**：
- **内容关联性分析**：分析检索内容间的逻辑关系
- **上下文补全算法**：自动补充缺失的关联信息
- **信息去重优化**：智能去除重复和冗余信息
- **逻辑顺序重组**：按照合理的逻辑顺序重新组织内容
- **完整性验证**：确保组装后内容的完整性和准确性

**技术要求**：
- 实现智能的内容关联分析算法
- 建立上下文补全的规则引擎
- 实现高效的去重和排序算法
- 建立内容完整性的验证机制

**输出物**：
- `context_assembler.py`：上下文组装器
- `content_relationship.json`：内容关联规则
- `deduplication_rules.json`：去重规则配置
- `completeness_validator.py`：完整性验证器

## 阶段七：数据验证与质量保证

### 7.1 RAG数据质量验证
**目标**：确保RAG系统数据的高质量和AI助手的准确性

**实施内容**：
- **完整性验证**：验证服务文档的完整性和自包含性
- **语义一致性检查**：确保文档内容的语义连贯性
- **metadata准确性验证**：验证metadata的准确性和完整性
- **分区效率测试**：测试分区策略的效率和准确性
- **缓存命中率测试**：验证缓存策略的有效性

**技术要求**：
- 建立RAG专门的质量评估体系
- 实现自动化的语义一致性检查
- 建立缓存效率的监控机制

**输出物**：
- `rag_quality_report.json`：RAG数据质量报告
- `semantic_validation.json`：语义验证结果
- `cache_efficiency.json`：缓存效率报告

### 7.2 AI助手功能验证
**目标**：验证AI助手的定价计算和问答准确性

**实施内容**：
- **定价计算验证**：验证复杂定价计算的准确性
- **问答质量测试**：测试AI助手对Azure服务问题的回答质量
- **计算过程展示验证**：验证计算过程可视化的准确性
- **边界情况测试**：测试极端和异常情况的处理
- **用户体验测试**：测试AI助手的易用性和友好性

**技术要求**：
- 建立标准的AI助手测试用例库
- 实现自动化的准确性验证
- 建立用户体验的评估标准

**输出物**：
- `ai_assistant_tests.json`：AI助手测试用例
- `accuracy_validation.json`：准确性验证报告
- `ux_evaluation.json`：用户体验评估

### 7.3 性能与缓存优化验证
**目标**：验证新一代RAG系统的性能优势

**实施内容**：
- **响应时间测试**：测试AI助手的响应速度
- **token使用效率**：验证输入输出token比例的优化效果
- **缓存命中率监控**：监控deepseek缓存的实际命中率
- **并发处理能力**：测试系统的并发查询处理能力
- **资源使用优化**：优化GPU和内存的使用效率

**技术要求**：
- 建立全面的性能监控体系
- 实现token使用的精确统计
- 建立缓存策略的动态优化机制

**输出物**：
- `performance_metrics.json`：性能指标报告
- `token_efficiency.json`：token使用效率报告
- `cache_optimization.json`：缓存优化建议

## 阶段八：多目标数据导出

### 8.1 混合RAG系统数据导出
**目标**：导出支持混合智能RAG系统的完整数据包

**实施内容**：
- **metadata索引包**：导出多维度metadata索引和过滤规则
- **多层级向量包**：导出产品级、功能级、场景级等不同维度的向量数据
- **传统RAG数据包**：导出embedding向量、rerank模型配置、检索索引
- **大上下文数据包**：导出完整服务文档、分区配置、上下文优化
- **混合策略配置包**：导出检索策略选择、成本控制、性能优化配置

**技术要求**：
- 支持多种向量存储格式（faiss、chromadb、qdrant）
- 实现向量数据的版本管理和增量更新
- 建立不同检索策略的配置管理
- 支持成本和性能的监控配置

**输出物**：
- `hybrid_rag_complete_package.tar.gz`：完整混合RAG数据包
- `metadata_index.json`：metadata索引数据
- `vector_embeddings/`：多维度向量数据目录
- `traditional_rag_config.json`：传统RAG配置
- `large_context_config.json`：大上下文配置
- `hybrid_strategy_config.json`：混合策略配置

### 8.2 CMS系统数据导出
**目标**：导出CMS内容管理系统专用的标准化数据

**实施内容**：
- **标准化JSON格式**：导出符合CMS标准的JSON数据
- **关系型数据导出**：导出SQL建表脚本和数据
- **CSV批量导入格式**：导出便于Excel操作的CSV文件
- **图片资源清单**：导出所有图片资源的路径和metadata
- **版本控制数据**：导出支持版本管理的数据格式

**技术要求**：
- 遵循CMS的数据格式标准
- 实现多种格式的自动转换
- 建立数据一致性验证机制

**输出物**：
- `cms_data_package.json`：CMS数据包
- `database_schema.sql`：数据库建表脚本
- `bulk_import.csv`：批量导入CSV文件
- `media_manifest.json`：媒体资源清单

### 8.3 API接口数据导出
**目标**：导出API接口和第三方集成专用的数据

**实施内容**：
- **RESTful API数据格式**：导出标准的API响应格式
- **OpenAPI规范文档**：生成完整的API文档
- **定价计算API**：导出定价计算的API接口数据
- **服务查询API**：导出服务信息查询的API数据
- **实时更新接口**：导出支持实时数据更新的接口格式

**技术要求**：
- 遵循REST API设计最佳实践
- 实现API版本管理
- 建立API数据的验证和测试机制

**输出物**：
- `api_data_package.json`：API数据包
- `openapi_spec.yaml`：OpenAPI规范文档
- `api_examples.json`：API调用示例
- `api_tests.json`：API测试用例

### 8.4 数据文档与工具导出
**目标**：导出完整的使用文档和辅助工具

**实施内容**：
- **RAG部署指南**：AI智能助手的部署和配置指南
- **CMS导入工具**：自动化的CMS数据导入工具
- **数据更新工具**：支持增量更新的数据同步工具
- **监控仪表板**：RAG系统性能监控的仪表板
- **故障排除手册**：常见问题和解决方案手册

**技术要求**：
- 编写详细的技术文档
- 开发用户友好的工具界面
- 建立完整的帮助系统

**输出物**：
- `rag_deployment_guide.md`：RAG部署指南
- `cms_import_tool.py`：CMS导入工具
- `data_sync_tool.py`：数据同步工具
- `monitoring_dashboard.html`：监控仪表板
- `troubleshooting_guide.md`：故障排除指南

## 项目管控与质量保证

### 里程碑设置
- **M1 (Week 1)**：完成HTML文件分析和解析引擎
- **M2 (Week 2)**：完成核心数据提取和清洗
- **M3 (Week 3)**：完成数据建模和关系构建
- **M4 (Week 4)**：完成定价计算器逻辑重建
- **M5 (Week 5-6)**：完成混合智能RAG数据准备（包含传统RAG + 大上下文）
- **M6 (Week 7)**：完成验证和多目标数据导出

### 风险控制
- **技术风险**：HTML结构复杂导致解析困难
  - *缓解措施*：分类处理，针对不同结构开发专门解析器
- **数据风险**：HTML中的数据不完整或格式不一致
  - *缓解措施*：多重验证，异常数据标记和人工复核
- **性能风险**：大量HTML文件处理性能不足
  - *缓解措施*：并行处理，分批加载，内存优化

### 质量标准
- **数据准确率**：关键定价数据准确率 > 95%
- **覆盖率**：核心服务覆盖率 > 90%
- **性能指标**：单服务处理时间 < 30秒
- **代码质量**：代码覆盖率 > 80%，文档完备率 > 90%

## 交付物清单

### 核心交付物
1. **源代码包**：完整的Python项目代码
2. **处理数据包**：所有处理后的结构化数据
3. **数据库文件**：包含所有数据的SQLite数据库
4. **导出工具包**：CMS导入工具和脚本

### 文档交付物
1. **技术文档**：系统架构和实现文档
2. **数据文档**：数据字典和使用指南
3. **操作手册**：部署和维护操作手册
4. **测试报告**：完整的测试和验证报告

### 配置交付物
1. **配置文件**：所有系统配置和参数
2. **部署脚本**：自动化部署和安装脚本
3. **监控配置**：性能监控和日志配置
4. **数据模板**：标准数据格式和模板

## 后续扩展规划

### 短期扩展（3个月内）
- **增量更新机制**：支持定期自动更新
- **多语言支持**：扩展英文版本的处理
- **高级计算器**：支持更复杂的组合定价

### 中期扩展（6个月内）
- **竞品比较**：集成其他云服务商的价格比较
- **成本优化建议**：基于使用模式的成本优化
- **预测分析**：价格趋势预测和分析

### 长期扩展（1年内）
- **智能推荐**：基于AI的服务推荐
- **实时同步**：与Azure官方API的实时同步
- **高级分析**：成本分析和优化的高级功能

## 特别说明：生产环境HTML文件处理

### 实际文件路径结构
**根据项目实际情况，HTML源文件已存放在：**
```
项目根目录/
└── current_prod_html/
    └── zh-cn/
        └── pricing/
            └── details/
                ├── {service1}/           # 如：virtual-machines
                ├── {service2}/           # 如：storage  
                ├── {service3}/           # 如：sql-database
                └── ...                   # 其他Azure服务
```

## 混合RAG实施策略详解

### 成本与效率平衡考虑

**查询类型分级策略：**
- **L0-FAQ快速响应**：预设问答，直接匹配（成本：0）
- **L1-基础信息查询**：metadata过滤 + 关键词匹配（成本：极低）
- **L2-产品对比查询**：embedding检索 + 简单排序（成本：低）
- **L3-复杂定价计算**：rerank优化 + 规则引擎（成本：中等）
- **L4-深度分析查询**：大上下文LLM + 完整推理（成本：高）

**动态策略选择算法：**
```
查询复杂度评估 → 用户等级检查 → 成本预算控制 → 策略路由选择
```

**成本控制机制：**
1. **Token使用预估**：查询前预估token消耗，超预算降级处理
2. **缓存优先策略**：优先使用cached结果，相似查询复用
3. **批量处理优化**：相关查询批量处理，减少API调用次数
4. **用户配额管理**：不同用户等级设置不同的L4查询配额

### 数据组织优化策略

**产品维度embedding策略：**
- **完整产品文档**：每个Azure服务一个完整embedding（避免切片损失）
- **功能模块embedding**：核心功能、定价、案例等分别embedding
- **关联服务embedding**：服务间依赖关系的专门向量化
- **用户场景embedding**：基于典型使用场景的内容组织

**metadata丰富化设计：**
```json
{
  "service_id": "virtual-machines",
  "categories": ["compute", "infrastructure"],
  "price_range": "low-medium-high",
  "complexity": "medium", 
  "use_cases": ["web-hosting", "data-processing"],
  "dependencies": ["storage", "networking"],
  "update_frequency": "monthly",
  "query_popularity": "high"
}
```

**检索效率优化：**
1. **预计算相似度**：热门服务间的相似度预计算缓存
2. **分层索引结构**：类别→服务→功能的层次化索引
3. **智能预加载**：根据查询模式预加载相关内容
4. **结果缓存策略**：基于查询模式的智能缓存

### AI助手集成考虑

**检索透明化要求：**
```
1. 查询意图识别 → 显示用户查询的理解结果
2. 检索路径记录 → 显示使用了哪些检索策略
3. 内容来源标注 → 标明信息来源和可信度
4. 计算过程展示 → 详细的定价计算步骤
5. 推荐逻辑说明 → 解释为什么推荐某些服务
```

**多模态展示支持：**
- **文本回答**：主要的回答内容
- **表格对比**：服务/价格对比表格
- **图表展示**：价格趋势、性能对比图表
- **代码示例**：API调用、配置示例
- **相关链接**：官方文档、详细说明链接
**根据项目实际情况，HTML源文件已存放在：**
```
项目根目录/
└── current_prod_html/
    └── zh-cn/
        └── pricing/
            └── details/
                ├── {service1}/           # 如：virtual-machines
                ├── {service2}/           # 如：storage  
                ├── {service3}/           # 如：sql-database
                └── ...                   # 其他Azure服务
```

### 文件处理配置
**AI实施时的关键配置参数：**
- **源文件根路径**：`./current_prod_html/zh-cn/pricing/details/`
- **文件扫描模式**：递归扫描所有子目录
- **文件类型过滤**：`*.html`, `*.htm`
- **编码假设**：UTF-8（如有问题需要自动检测）
- **服务ID提取**：从目录名或文件名提取服务标识

### HTML文件特点分析
1. **统一模板结构**：所有文件应该基于相同或类似的模板生成
2. **中文内容为主**：需要重点处理中文文本的分词和清洗
3. **定价表格标准化**：Azure定价页面通常有标准化的表格结构
4. **服务分类明确**：通过目录结构可以初步确定服务分类

### 处理优先级策略
**基于Azure服务的商业重要性排序：**

**Tier 1 - 核心计算和存储（优先处理）**
- `virtual-machines/` - 虚拟机
- `storage/` - 存储服务
- `app-service/` - 应用服务
- `sql-database/` - SQL数据库

**Tier 2 - 重要基础服务**
- `cosmos-db/` - Cosmos数据库
- `virtual-network/` - 虚拟网络
- `load-balancer/` - 负载均衡器
- `cdn/` - 内容分发网络

**Tier 3 - 专业服务**
- `cognitive-services/` - 认知服务
- `functions/` - 无服务器函数
- `kubernetes-service/` - 容器服务
- `monitor/` - 监控服务

### 文件扫描和预处理要求
1. **递归扫描**：扫描 `details/` 下所有子目录的HTML文件
2. **文件完整性检查**：验证HTML文件是否完整和有效
3. **服务映射建立**：目录名 → 服务ID → 服务显示名称
4. **重复文件检测**：检测可能的重复或版本文件
5. **文件大小分析**：识别异常大小的文件（可能包含更多数据或存在问题）

### 混合智能RAG系统要求
**基于用户的混合RAG理念，平衡信息完整性与成本效率：**

**核心设计原则：**
1. **metadata驱动过滤**：通过丰富的产品metadata实现精准预过滤，大幅减少搜索空间
2. **智能向量化策略**：以产品/服务为单位进行有意义的embedding，避免盲目chunk切片
3. **多层级检索架构**：根据查询复杂度智能选择最优检索策略
4. **成本效率平衡**：动态选择传统RAG vs 大上下文方案，控制API调用成本
5. **信息损耗最小化**：在保持检索效率的同时，最大程度保持信息完整性

**检索策略分层：**
- **L1-快速过滤层**：metadata索引 + 关键词匹配（毫秒级响应）
- **L2-语义检索层**：产品级embedding + 向量相似度（100ms级响应）
- **L3-精准重排层**：rerank模型 + 相关性优化（500ms级响应）
- **L4-完整上下文层**：大窗口LLM + 完整信息（秒级响应，高精度场景）

**成本控制策略：**
- **查询分类路由**：简单查询走L1-L2，复杂查询走L3-L4
- **用户等级区分**：普通用户限制L4使用，VIP用户享受完整服务
- **缓存优化**：热点查询缓存，相似查询复用结果
- **token使用监控**：实时监控API调用成本，动态调整策略

**数据组织策略：**
- **产品维度完整性**：每个Azure服务保持完整的信息描述
- **功能维度细分**：核心功能、定价、使用场景等分别向量化
- **关联关系网络**：构建服务间的依赖和推荐关系
- **动态更新机制**：支持增量更新和版本管理

**AI助手集成要求：**
- **检索过程透明化**：详细记录检索路径和决策过程
- **计算步骤可视化**：定价计算的每个步骤都可追踪和解释
- **多模态交互支持**：文本、表格、图表等多种形式的信息展示
- **个性化推荐**：基于用户历史和偏好的智能推荐

---

**文档版本**：v1.3  
**最后更新**：2025-06-18  
**项目代号**：AzureCNArchaeologist  
**负责团队**：Azure定价重建项目组  
**特别说明**：混合智能RAG系统的平衡方案版本