[{"os": "Anomaly Detector", "region": "east-china", "tableIDs": ["#Anomaly_Detector"]}, {"os": "Anomaly Detector", "region": "north-china", "tableIDs": ["#Anomaly_Detector"]}, {"os": "Anomaly Detector", "region": "north-china2", "tableIDs": ["#Anomaly_Detector"]}, {"os": "Cognitive Services", "region": "east-china2", "tableIDs": ["#cognitive-services-table-speech-services", "#cognitive-services-table-speech-services-neural", "#cognitive-services-table-speech-services-north"]}, {"os": "Cognitive Services", "region": "north-china3", "tableIDs": ["#cognitive-services-table-speech-services", "#cognitive-services-table-speech-services-neural", "#cognitive-services-table-speech-services-north", "#cognitive-services-table-speech-commitment-tiers-north2east2", "#cognitive-services1", "#cognitive-services-table-training", "#cognitive-services5"]}, {"os": "Cognitive Services", "region": "east-china", "tableIDs": ["#cognitive-services-table-speech-services", "#cognitive-services-table-speech-services-neural", "#cognitive-services-table-speech-services-east2", "#cognitive-services-table-speech-servicesv1", "#cognitive-services1", "#cognitive-services2", "#cognitive-services3", "#cognitive-services4", "#cognitive-services5", "#cognitive-services-table-admitlevel-nor2east2", "#cognitive-services-table-speech-commitment-tiers-north2east2", "#cognitive-services-LanguageService-nor2east2", "#cognitive-services-table-speech-services-tier-nor2east2", "#cognitive-services-north3"]}, {"os": "Cognitive Services", "region": "north-china2", "tableIDs": ["#cognitive-services-table-speech-services", "#cognitive-services-table-speech-servicesv1", "#cognitive-services-table-speech-services-east2", "#cognitive-services-table-speech-services-north", "#cognitive-services1", "#cognitive-services3", "#cognitive-services4", "#cognitive-services5", "#cognitive-services2", "#cognitive-services-north3"]}, {"os": "Cognitive Services", "region": "north-china", "tableIDs": ["#cognitive-services-table-speech-services-neural", "#cognitive-services-table-speech-services-east2", "#cognitive-services-table-speech-servicesv1", "#cognitive-services1", "#cognitive-services3", "#cognitive-services4", "#cognitive-services5", "#cognitive-services-table-admitlevel-nor2east2", "#cognitive-services-table-speech-commitment-tiers-north2east2", "#cognitive-services-LanguageService-nor2east2", "#cognitive-services-table-speech-services-tier-nor2east2", "#cognitive-services-north3"]}, {"os": "Data Factory SSIS", "region": "north-china", "tableIDs": ["#data-factory-ssis-standard-a1v2-a8v2", "#data-factory-ssis-enterprise-a1v2-a8v2", "#data-factory-ssis-standard-d1v2-d4v2", "#data-factory-ssis-enterprise-d1v2-d4v2", "#data-factory-ssis-standard-e2v3-e64v3", "#data-factory-ssis-enterprise-e2v3-e64v3", "#data-factory-ssis-standard-d2v3-d64v3", "#data-factory-ssis-enterprise-d2v3-d64v3"]}, {"os": "Data Factory SSIS", "region": "east-china", "tableIDs": ["#data-factory-ssis-standard-a1v2-a8v2", "#data-factory-ssis-enterprise-a1v2-a8v2", "#data-factory-ssis-standard-d1v2-d4v2", "#data-factory-ssis-enterprise-d1v2-d4v2", "#data-factory-ssis-standard-e2v3-e64v3", "#data-factory-ssis-enterprise-e2v3-e64v3", "#data-factory-ssis-standard-d2v3-d64v3", "#data-factory-ssis-enterprise-d2v3-d64v3"]}, {"os": "Data Factory Data Pipeline", "region": "north-china2", "tableIDs": []}, {"os": "Data Factory Data Pipeline", "region": "north-china", "tableIDs": ["#data-factory-data-pipeline-features", "#data-factory-data-pipeline-operation", "#Data-Flow-Execution-and-Debugging"]}, {"os": "Data Factory Data Pipeline", "region": "east-china", "tableIDs": ["#data-factory-data-pipeline-features", "#data-factory-data-pipeline-operation", "#Data-Flow-Execution-and-Debugging"]}, {"os": "Power BI Embedded", "region": "north-china3", "tableIDs": ["#power-bi-embedded-table-a7a8"]}, {"os": "Power BI Embedded", "region": "east-china2", "tableIDs": ["#power-bi-embedded-table-a7a8"]}, {"os": "Power BI Embedded", "region": "north-china2", "tableIDs": ["#power-bi-embedded-table-a7a8"]}, {"os": "Power BI Embedded", "region": "east-china", "tableIDs": ["#power-bi-embedded-table-hide-a7a8"]}, {"os": "Power BI Embedded", "region": "north-china", "tableIDs": ["#power-bi-embedded-table-hide-a7a8"]}, {"os": "Azure Database for MySQL", "region": "north-china", "tableIDs": ["#Azure_Database_For_MySQL1", "#Azure_Database_For_MySQL2", "#Azure_Database_For_MySQL3", "#Azure_Database_For_MySQL4", "#Azure_Database_For_MySQL5", "#Azure_Database_For_MySQL6", "#Azure_Database_For_MySQL7", "#Azure_Database_For_MySQL8", "#Azure_Database_For_MySQL20", "#Azure_Database_For_MySQL18", "#Azure_Database_For_MySQL10", "#Azure_Database_For_MySQL11", "#Azure_Database_For_MySQL12", "#Azure_Database_For_MySQL14", "#Azure_Database_For_MySQL19", "#Azure_Database_For_MySQL21", "#Azure_Database_For_MySQL17", "#Azure_Database_For_MySQL22", "#Azure_Database_For_MySQL9", "#Azure_Database_For_MySQL13", "Azure_Database_For_MySQL30", "#Azure_Database_For_MySQL31", "#Azure_Database_For_MySQL32", "#Azure_Database_For_MySQL34", "#Azure_Database_For_MySQL35", "#Azure_Database_For_MySQL_IOPS", "#Azure_Database_For_MySQL_IOPS_East3"]}, {"os": "Azure Database for MySQL", "region": "north-china2", "tableIDs": ["#Azure_Database_For_MySQL8", "#Azure_Database_For_MySQL10", "#Azure_Database_For_MySQL12", "#Azure_Database_For_MySQL14", "#Azure_Database_For_MySQL7", "#Azure_Database_For_MySQL18", "#Azure_Database_For_MySQL16", "#Azure_Database_For_MySQL15", "#Azure_Database_For_MySQL20", "#Azure_Database_For_MySQL31", "#Azure_Database_For_MySQL32", "#Azure_Database_For_MySQL34", "#Azure_Database_For_MySQL35", "#Azure_Database_For_MySQL_IOPS"]}, {"os": "Azure Database for MySQL", "region": "north-china3", "tableIDs": ["#Azure_Database_For_MySQL6", "#Azure_Database_For_MySQL4", "#Azure_Database_For_MySQL19", "#Azure_Database_For_MySQL17", "#Azure_Database_For_MySQL9", "#Azure_Database_For_MySQL13", "#Azure_Database_For_MySQL30", "#Azure_Database_For_MySQL33", "#Azure_Database_For_MySQL36", "#Azure_Database_For_MySQL37", "#Azure_Database_For_MySQL38", "#Azure_Database_For_MySQL39", "#Azure_Database_For_MySQL40", "#Azure_Database_For_MySQL41", "#Azure_Database_For_MySQL42", "#Azure_Database_For_MySQL43", "#Azure_Database_For_MySQL44", "#Azure_Database_For_MySQL45", "#Azure_Database_For_MySQL32", "#Azure_Database_For_MySQL34", "#Azure_Database_For_MySQL31", "#Azure_Database_For_MySQL35", "#Azure_Database_For_MySQL_IOPS_East3"]}, {"os": "Azure Database for MySQL", "region": "east-china3", "tableIDs": ["#Azure_Database_For_MySQL6", "#Azure_Database_For_MySQL4", "#Azure_Database_For_MySQL19", "#Azure_Database_For_MySQL17", "#Azure_Database_For_MySQL9", "#Azure_Database_For_MySQL13", "#Azure_Database_For_MySQL30", "#Azure_Database_For_MySQL33", "#Azure_Database_For_MySQL36", "#Azure_Database_For_MySQL37", "#Azure_Database_For_MySQL38", "#Azure_Database_For_MySQL39", "#Azure_Database_For_MySQL40", "#Azure_Database_For_MySQL41", "#Azure_Database_For_MySQL42", "#Azure_Database_For_MySQL43", "#Azure_Database_For_MySQL44", "#Azure_Database_For_MySQL45", "#Azure_Database_For_MySQL32", "#Azure_Database_For_MySQL34", "#Azure_Database_For_MySQL31", "#Azure_Database_For_MySQL35", "#Azure_Database_For_MySQL_IOPS"]}, {"os": "Azure Database for MySQL", "region": "east-china", "tableIDs": ["#Azure_Database_For_MySQL1", "#Azure_Database_For_MySQL2", "#Azure_Database_For_MySQL3", "#Azure_Database_For_MySQL4", "#Azure_Database_For_MySQL5", "#Azure_Database_For_MySQL6", "#Azure_Database_For_MySQL7", "#Azure_Database_For_MySQL8", "#Azure_Database_For_MySQL20", "#Azure_Database_For_MySQL18", "#Azure_Database_For_MySQL10", "#Azure_Database_For_MySQL12", "#Azure_Database_For_MySQL14", "#Azure_Database_For_MySQL19", "#Azure_Database_For_MySQL21", "#Azure_Database_For_MySQL17", "#Azure_Database_For_MySQL22", "#Azure_Database_For_MySQL9", "#Azure_Database_For_MySQL13", "#Azure_Database_For_MySQL30", "#Azure_Database_For_MySQL31", "#Azure_Database_For_MySQL32", "#Azure_Database_For_MySQL34", "#Azure_Database_For_MySQL35", "#Azure_Database_For_MySQL_IOPS", "#Azure_Database_For_MySQL_IOPS_East3"]}, {"os": "Azure Database for MySQL", "region": "east-china2", "tableIDs": ["#Azure_Database_For_MySQL8", "#Azure_Database_For_MySQL10", "#Azure_Database_For_MySQL12", "#Azure_Database_For_MySQL14", "#Azure_Database_For_MySQL7", "#Azure_Database_For_MySQL18", "#Azure_Database_For_MySQL16", "#Azure_Database_For_MySQL15", "#Azure_Database_For_MySQL20", "#Azure_Database_For_MySQL31", "#Azure_Database_For_MySQL32", "#Azure_Database_For_MySQL34", "#Azure_Database_For_MySQL35", "#Azure_Database_For_MySQL_IOPS"]}, {"os": "Storage Files", "region": "east-china", "tableIDs": ["#storage-files-date-storage-price-table2", "#storage-files-date-storage-price-table3", "#storage-files-date-storage-price-table4", "#storage-files-date-storage-price-table-new1first", "#storage-files-date-storage-price-table-new1", "#storage-files-date-storage-price-table-new2", "#storage-files-date-storage-price-table-new3", "#storage-files-date-storage-price-table-new4s", "#storage-files-date-storage-price-table-new1-north3", "#storage-files-date-storage-price-table-new1-east3", "#storage-files-date-storage-price-table-new2-north3", "#storage-files-date-storage-price-table-new2-east3", "#storage-files-data-storage-transactions-transfer-north3", "#storage-files-data-storage-transactions-transfer-east3", "#storage-files-date-storage-price-GZRS-table-china-north3", "#storage-files-date-storage-price-ZRS-table-china-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"]}, {"os": "Storage Files", "region": "north-china", "tableIDs": ["#storage-files-date-storage-price-table2", "#storage-files-date-storage-price-table3", "#storage-files-date-storage-price-table4", "#storage-files-date-storage-price-table-new1first", "#storage-files-date-storage-price-table-new1", "#storage-files-date-storage-price-table-new2", "#storage-files-date-storage-price-table-new3", "#storage-files-date-storage-price-table-new4s", "#storage-files-date-storage-price-table-new1-north3", "#storage-files-date-storage-price-table-new1-east3", "#storage-files-date-storage-price-table-new2-north3", "#storage-files-date-storage-price-table-new2-east3", "#storage-files-data-storage-transactions-transfer-north3", "#storage-files-data-storage-transactions-transfer-east3", "#storage-files-date-storage-price-GZRS-table-china-north3", "#storage-files-date-storage-price-ZRS-table-china-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"]}, {"os": "Storage Files", "region": "east-china2", "tableIDs": ["#storage-files-date-storage-price-table", "#storage-files-date-storage-price-table-new1s", "#storage-files-date-storage-price-table-new2s", "#storage-files-date-storage-price-table-new3s", "#storage-files-date-storage-price-table-new1-north3", "#storage-files-date-storage-price-table-new1-east3", "#storage-files-date-storage-price-table-new2-north3", "#storage-files-date-storage-price-table-new2-east3", "#storage-files-data-storage-transactions-transfer-north3", "#storage-files-data-storage-transactions-transfer-east3", "#storage-files-date-storage-price-GZRS-table-china-north3", "#storage-files-date-storage-price-ZRS-table-china-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"]}, {"os": "Storage Files", "region": "north-china2", "tableIDs": ["#storage-files-date-storage-price-table", "#storage-files-date-storage-price-table-new1s", "#storage-files-date-storage-price-table-new2s", "#storage-files-date-storage-price-table-new3s", "#storage-files-date-storage-price-table-new1-north3", "#storage-files-date-storage-price-table-new1-east3", "#storage-files-date-storage-price-table-new2-north3", "#storage-files-date-storage-price-table-new2-east3", "#storage-files-data-storage-transactions-transfer-north3", "#storage-files-data-storage-transactions-transfer-east3", "#storage-files-date-storage-price-GZRS-table-china-north3", "#storage-files-date-storage-price-ZRS-table-china-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"]}, {"os": "Storage Files", "region": "east-china3", "tableIDs": ["#storage-files-date-storage-price-table", "#storage-files-date-storage-price-table-new1-east3", "#storage-files-date-storage-price-table-new1first", "#storage-files-date-storage-price-table-new1s", "#storage-files-date-storage-price-table-new2-east3", "#storage-files-date-storage-price-table-new2", "#storage-files-date-storage-price-table-new2s", "#storage-files-date-storage-price-table-new3", "#storage-files-date-storage-price-table-new3s", "#storage-files-data-storage-transactions-transfer", "#storage-files-date-storage-price-table-new4s", "#storage-files-data-storage-price-zrs-table", "#storage-files-data-storage-transactions-transfer-east3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3", "#storage-files-date-storage-price-ZRS-table-china-north3", "#storage-files-date-storage-price-GZRS-table-china-north3"]}, {"os": "Storage Files", "region": "north-china3", "tableIDs": ["#storage-files-date-storage-price-table", "#storage-files-date-storage-price-table-new1-east3", "#storage-files-date-storage-price-table-new1first", "#storage-files-date-storage-price-table-new1s", "#storage-files-date-storage-price-table-new2-east3", "#storage-files-date-storage-price-table-new2", "#storage-files-date-storage-price-table-new2s", "#storage-files-date-storage-price-table-new3", "#storage-files-date-storage-price-table-new3s", "#storage-files-data-storage-transactions-transfer", "#storage-files-date-storage-price-table-new4s", "#storage-files-data-storage-price-zrs-table", "#storage-files-data-storage-transactions-transfer-east3", "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"]}, {"os": "API Management", "region": "north-china", "tableIDs": ["#API-Management-preview", "#API-Management-gateway"]}, {"os": "API Management", "region": "east-china", "tableIDs": ["#API-Management-preview", "#API-Management-gateway"]}, {"os": "API Management", "region": "north-china2", "tableIDs": ["#API-Management-preview2"]}, {"os": "API Management", "region": "east-china2", "tableIDs": ["#API-Management-preview2"]}, {"os": "API Management", "region": "north-china3", "tableIDs": ["#API-Management-preview2"]}, {"os": "Azure Cosmos DB", "region": "east-china3", "tableIDs": ["#cosmos-db-3", "#cosmos-db-2", "#cosmos-db-1", "#cosmos-3", "#cosmos-7", "#cosmos-11", "#cosmos-3-2", "#cosmos-3-3"]}, {"os": "Azure Cosmos DB", "region": "east-china2", "tableIDs": ["#cosmos-db-3-n3", "#cosmos-db-2-n3", "#cosmos-db-1-n3", "#cosmos-db-s-n3", "#cosmos-11-3", "#cosmos-11-2", "#cosmos-11-1", "#cosmos-11-4"]}, {"os": "Azure Cosmos DB", "region": "east-china", "tableIDs": ["#cosmos-db-3-n3", "#cosmos-db-2-n3", "#cosmos-db-1-n3", "#cosmos-db-s-n3", "#cosmos-11-3", "#cosmos-11-2", "#cosmos-11-1", "#cosmos-11-4"]}, {"os": "Azure Cosmos DB", "region": "north-china3", "tableIDs": ["#cosmos-db-3", "#cosmos-db-2", "#cosmos-db-1", "#cosmos-3", "#cosmos-7", "#cosmos-11", "#cosmos-3-2", "#cosmos-3-3"]}, {"os": "Azure Cosmos DB", "region": "north-china2", "tableIDs": ["#cosmos-db-3-n3", "#cosmos-db-2-n3", "#cosmos-db-1-n3", "#cosmos-db-s-n3", "#cosmos-11-3", "#cosmos-11-2", "#cosmos-11-1", "#cosmos-11-4"]}, {"os": "Azure Cosmos DB", "region": "north-china", "tableIDs": ["#cosmos-db-3-n3", "#cosmos-db-2-n3", "#cosmos-db-1-n3", "#cosmos-db-s-n3", "#cosmos-11-3", "#cosmos-11-2", "#cosmos-11-1", "#cosmos-11-4"]}, {"os": "Machine Learning Server", "region": "north-china", "tableIDs": ["#vm-table5-1-3", "#vm-table5-1-5", "#vm-table5-1-6", "#vm-table5-3-1", "#vm-table5-3-2", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-4-1", "#vm-table5-2-1-region2", "#vms-table5-1-3", "#vms-table5-1-5", "#vms-table5-1-6", "#vms-table5-3-1", "#vms-table5-3-2", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vms-table5-4-1", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table5-1-new", "#vm-table5-2-new", "#vm-table5-3-new", "#vm-table5-4-new", "#vm-table5-5-new", "#vm-table5-6-new", "#vm-table5-7-new", "#vm-table5-8-new", "#vm-table-Constrained-5-1"]}, {"os": "Machine Learning Server", "region": "east-china2", "tableIDs": ["#vm-table5-1-4", "#vm-table5-3-1", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-2-1", "#vms-table5-1-4", "#vms-table5-3-1", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table-Constrained-5-2"]}, {"os": "Machine Learning Server", "region": "east-china", "tableIDs": ["#vm-table5-1-3", "#vm-table5-1-5", "#vm-table5-1-6", "#vm-table5-3-1", "#vm-table5-3-2", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-4-1", "#vm-table5-2-1-region2", "#vms-table5-1-3", "#vms-table5-1-5", "#vms-table5-1-6", "#vms-table5-3-1", "#vms-table5-3-2", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vms-table5-4-1", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table5-1-new", "#vm-table5-2-new", "#vm-table5-3-new", "#vm-table5-4-new", "#vm-table5-5-new", "#vm-table5-6-new", "#vm-table5-7-new", "#vm-table5-8-new", "#vm-table-Constrained-5-1"]}, {"os": "Machine Learning Server", "region": "east-china3", "tableIDs": ["#vm-table5-1-3", "#vm-table5-1-5", "#vm-table5-1-6", "#vm-table5-3-1", "#vm-table5-3-2", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-4-1", "#vm-table5-2-1-region2", "#vms-table5-1-3", "#vms-table5-1-5", "#vms-table5-1-6", "#vms-table5-3-1", "#vms-table5-3-2", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vms-table5-4-1", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table5-1-new", "#vm-table5-2-new", "#vm-table5-3-new", "#vm-table5-4-new", "#vm-table5-5-new", "#vm-table5-6-new", "#vm-table5-7-new", "#vm-table5-8-new", "#vm-table-Constrained-5-1", "#vm-table5-1-4", "#vm-table5-3-1", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-2-1", "#vms-table5-1-4", "#vms-table5-3-1", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table-Constrained-5-2", "#vm-table5-1-1", "#vm-table5-1-2", "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms"]}, {"os": "Machine Learning Server", "region": "north-china3", "tableIDs": ["#vm-table5-1-3", "#vm-table5-1-5", "#vm-table5-1-6", "#vm-table5-3-1", "#vm-table5-3-2", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-4-1", "#vm-table5-2-1-region2", "#vms-table5-1-3", "#vms-table5-1-5", "#vms-table5-1-6", "#vms-table5-3-1", "#vms-table5-3-2", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vms-table5-4-1", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table5-1-new", "#vm-table5-2-new", "#vm-table5-3-new", "#vm-table5-4-new", "#vm-table5-5-new", "#vm-table5-6-new", "#vm-table5-7-new", "#vm-table5-8-new", "#vm-table-Constrained-5-1", "#vm-table5-1-4", "#vm-table5-3-1", "#vm-table-machinelearning-memoryprioritization-d15v2", "#vm-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-2-1", "#vms-table5-1-4", "#vms-table5-3-1", "#vms-table-machinelearning-memoryprioritization-d15v2", "#vms-table-machinelearning-memoryprioritization-ds15v2", "#vm-table5-1-1-ml", "#vm-table5-1-2-ml", "#vm-table5-1-3-ml", "#vm-table5-1-4-ml", "#vm-table5-1-5-ml", "#vm-table5-1-6-ml", "#vm-table-machinelearning-computingprioritization-f1-f16-ml", "#vm-table5-2-1-ml", "#vm-table5-3-1-ml", "#vm-table5-3-2-ml", "#vm-table-machinelearning-memoryprioritization-d15v2-ml", "#vm-table5-3-3-ml", "#vm-table-machinelearning-memoryprioritization-ds15v2-ml", "#vm-table-Constrained-5-2", "#vm-table5-1-1", "#vm-table5-1-2", "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms"]}, {"os": "Azure_Data_Lake_Storage_Gen", "region": "north-china", "tableIDs": ["#Azure-Data-Lake-Storage-Gen1-1", "#Azure-Data-Lake-Storage-Gen1-2", "#Azure-Data-Lake-Storage-Gen1-3", "#Azure-Data-Lake-Storage-Gen1-4", "#Azure-Data-Lake-Storage-Gen2-1", "#Azure-Data-Lake-Storage-Gen2-2", "#Azure-Data-Lake-Storage-Gen2-3", "#Azure-Data-Lake-Storage-Gen2-4", "#Azure-Data-Lake-Storage-Gen1-1-north3", "#Azure-Data-Lake-Storage-Gen1-2-north3", "#Azure-Data-Lake-Storage-Gen1-3-north3", "#Azure-Data-Lake-Storage-Gen1-4-north3", "#Azure-Data-Lake-Storage-Gen2-1-north3", "#Azure-Data-Lake-Storage-Gen2-2-north3", "#Azure-Data-Lake-Storage-Gen2-3-north3", "#Azure-Data-Lake-Storage-Gen2-4-north3", "#Azure-Data-Lake-Storage-Gen1-1-east3", "#Azure-Data-Lake-Storage-Gen1-2-east3", "#Azure-Data-Lake-Storage-Gen1-3-east3", "#Azure-Data-Lake-Storage-Gen1-4-east3", "#Azure-Data-Lake-Storage-Gen2-1-east3", "#Azure-Data-Lake-Storage-Gen2-2-east3", "#Azure-Data-Lake-Storage-Gen2-3-east3", "#Azure-Data-Lake-Storage-Gen2-4-east3"]}, {"os": "Azure_Data_Lake_Storage_Gen", "region": "east-china", "tableIDs": ["#Azure-Data-Lake-Storage-Gen1-1", "#Azure-Data-Lake-Storage-Gen1-2", "#Azure-Data-Lake-Storage-Gen1-3", "#Azure-Data-Lake-Storage-Gen1-4", "#Azure-Data-Lake-Storage-Gen2-1", "#Azure-Data-Lake-Storage-Gen2-2", "#Azure-Data-Lake-Storage-Gen2-3", "#Azure-Data-Lake-Storage-Gen2-4", "#Azure-Data-Lake-Storage-Gen1-1-north3", "#Azure-Data-Lake-Storage-Gen1-2-north3", "#Azure-Data-Lake-Storage-Gen1-3-north3", "#Azure-Data-Lake-Storage-Gen1-4-north3", "#Azure-Data-Lake-Storage-Gen2-1-north3", "#Azure-Data-Lake-Storage-Gen2-2-north3", "#Azure-Data-Lake-Storage-Gen2-3-north3", "#Azure-Data-Lake-Storage-Gen2-4-north3", "#Azure-Data-Lake-Storage-Gen1-1-east3", "#Azure-Data-Lake-Storage-Gen1-2-east3", "#Azure-Data-Lake-Storage-Gen1-3-east3", "#Azure-Data-Lake-Storage-Gen1-4-east3", "#Azure-Data-Lake-Storage-Gen2-1-east3", "#Azure-Data-Lake-Storage-Gen2-2-east3", "#Azure-Data-Lake-Storage-Gen2-3-east3", "#Azure-Data-Lake-Storage-Gen2-4-east3"]}, {"os": "Azure_Data_Lake_Storage_Gen", "region": "north-china2", "tableIDs": ["#Azure-Data-Lake-Storage-Gen1-1-1", "#Azure-Data-Lake-Storage-Gen1-2-2", "#Azure-Data-Lake-Storage-Gen1-3-3", "#Azure-Data-Lake-Storage-Gen1-4-4", "#Azure-Data-Lake-Storage-Gen2-1-1", "#Azure-Data-Lake-Storage-Gen2-2-2", "#Azure-Data-Lake-Storage-Gen2-3-3", "#Azure-Data-Lake-Storage-Gen2-4-4", "#Azure-Data-Lake-Storage-Gen1-1-north3", "#Azure-Data-Lake-Storage-Gen1-2-north3", "#Azure-Data-Lake-Storage-Gen1-3-north3", "#Azure-Data-Lake-Storage-Gen1-4-north3", "#Azure-Data-Lake-Storage-Gen2-1-north3", "#Azure-Data-Lake-Storage-Gen2-2-north3", "#Azure-Data-Lake-Storage-Gen2-3-north3", "#Azure-Data-Lake-Storage-Gen2-4-north3", "#Azure-Data-Lake-Storage-Gen1-1-east3", "#Azure-Data-Lake-Storage-Gen1-2-east3", "#Azure-Data-Lake-Storage-Gen1-3-east3", "#Azure-Data-Lake-Storage-Gen1-4-east3", "#Azure-Data-Lake-Storage-Gen2-1-east3", "#Azure-Data-Lake-Storage-Gen2-2-east3", "#Azure-Data-Lake-Storage-Gen2-3-east3", "#Azure-Data-Lake-Storage-Gen2-4-east3"]}, {"os": "Azure_Data_Lake_Storage_Gen", "region": "east-china2", "tableIDs": ["#Azure-Data-Lake-Storage-Gen1-1-1", "#Azure-Data-Lake-Storage-Gen1-2-2", "#Azure-Data-Lake-Storage-Gen1-3-3", "#Azure-Data-Lake-Storage-Gen1-4-4", "#Azure-Data-Lake-Storage-Gen2-1-1", "#Azure-Data-Lake-Storage-Gen2-2-2", "#Azure-Data-Lake-Storage-Gen2-3-3", "#Azure-Data-Lake-Storage-Gen2-4-4", "#Azure-Data-Lake-Storage-Gen1-1-north3", "#Azure-Data-Lake-Storage-Gen1-2-north3", "#Azure-Data-Lake-Storage-Gen1-3-north3", "#Azure-Data-Lake-Storage-Gen1-4-north3", "#Azure-Data-Lake-Storage-Gen2-1-north3", "#Azure-Data-Lake-Storage-Gen2-2-north3", "#Azure-Data-Lake-Storage-Gen2-3-north3", "#Azure-Data-Lake-Storage-Gen2-4-north3", "#Azure-Data-Lake-Storage-Gen1-1-east3", "#Azure-Data-Lake-Storage-Gen1-2-east3", "#Azure-Data-Lake-Storage-Gen1-3-east3", "#Azure-Data-Lake-Storage-Gen1-4-east3", "#Azure-Data-Lake-Storage-Gen2-1-east3", "#Azure-Data-Lake-Storage-Gen2-2-east3", "#Azure-Data-Lake-Storage-Gen2-3-east3", "#Azure-Data-Lake-Storage-Gen2-4-east3"]}, {"os": "Azure_Data_Lake_Storage_Gen", "region": "north-china3", "tableIDs": ["#Azure-Data-Lake-Storage-Gen1-1-east3", "#Azure-Data-Lake-Storage-Gen1-2-east3", "#Azure-Data-Lake-Storage-Gen1-3-east3", "#Azure-Data-Lake-Storage-Gen1-4-east3", "#Azure-Data-Lake-Storage-Gen2-1-east3", "#Azure-Data-Lake-Storage-Gen2-2-east3", "#Azure-Data-Lake-Storage-Gen2-3-east3", "#Azure-Data-Lake-Storage-Gen2-4-east3", "#Azure-Data-Lake-Storage-Gen1-1-1", "#Azure-Data-Lake-Storage-Gen1-2-2", "#Azure-Data-Lake-Storage-Gen1-3-3", "#Azure-Data-Lake-Storage-Gen1-4-4", "#Azure-Data-Lake-Storage-Gen2-1-1", "#Azure-Data-Lake-Storage-Gen2-2-2", "#Azure-Data-Lake-Storage-Gen2-3-3", "#Azure-Data-Lake-Storage-Gen2-4-4", "#Azure-Data-Lake-Storage-Gen1-1", "#Azure-Data-Lake-Storage-Gen1-2", "#Azure-Data-Lake-Storage-Gen1-3", "#Azure-Data-Lake-Storage-Gen1-4", "#Azure-Data-Lake-Storage-Gen2-1", "#Azure-Data-Lake-Storage-Gen2-2", "#Azure-Data-Lake-Storage-Gen2-3", "#Azure-Data-Lake-Storage-Gen2-4"]}, {"os": "Azure_Data_Lake_Storage_Gen", "region": "east-china3", "tableIDs": ["#Azure-Data-Lake-Storage-Gen2-3-east3", "#Azure-Data-Lake-Storage-Gen2-4-north3", "#Azure-Data-Lake-Storage-Gen2-2-north3", "#Azure-Data-Lake-Storage-Gen1-1-1", "#Azure-Data-Lake-Storage-Gen1-2-2", "#Azure-Data-Lake-Storage-Gen1-3-3", "#Azure-Data-Lake-Storage-Gen1-4-4", "#Azure-Data-Lake-Storage-Gen2-1-1", "#Azure-Data-Lake-Storage-Gen2-2-2", "#Azure-Data-Lake-Storage-Gen2-3-3", "#Azure-Data-Lake-Storage-Gen2-4-4", "#Azure-Data-Lake-Storage-Gen1-1", "#Azure-Data-Lake-Storage-Gen1-2", "#Azure-Data-Lake-Storage-Gen1-3", "#Azure-Data-Lake-Storage-Gen1-4", "#Azure-Data-Lake-Storage-Gen2-1", "#Azure-Data-Lake-Storage-Gen2-2", "#Azure-Data-Lake-Storage-Gen2-3", "#Azure-Data-Lake-Storage-Gen2-4", "#Azure-Data-Lake-Storage-Gen2-1-north3", "#Azure-Data-Lake-Storage-Gen1-1-north3", "#Azure-Data-Lake-Storage-Gen1-3-north3", "#Azure-Data-Lake-Storage-Gen1-2-north3", "#Azure-Data-Lake-Storage-Gen1-4-north3"]}, {"os": "databricks", "region": "east-china", "tableIDs": ["#databricks-data-analysis", "#databricks-data-analysis1", "#databricks-data-analysis2", "#databricks-data-analysis3", "#databricks-data-analysis4", "#databricks-data-analysis5", "#databricks-data-analysis6", "#databricks-data-analysis7", "#databricks-data-analysis8", "#databricks-data-analysis9", "#databricks-data-analysis10", "#databricks-data-analysis11", "#databricks-data-analysis12", "#databricks-data-analysis13", "#databricks-data-analysis14", "#databricks-data-analysis15", "#databricks-data-analysis16", "#databricks-data-analysis17", "#databricks-data-analysis18", "#databricks-data-analysis19", "#databricks-data-analysis20", "#databricks-data-analysis21", "#databricks-data-analysis22", "#databricks-data-analysis23", "#databricks-data-analysis24", "#databricks-data-analysisN-1", "#databricks-data-analysisN-2", "#databricks-data-analysisN-3", "#databricks-General-DSV2", "#databricks-General-Dv2", "#databricks-General-DSv3", "#databricks-Memory-DSv2", "#databricks-Memory-Dv2", "#databricks-Memory-Esv3", "#databricks-Compute-Fsv2", "#databricks-Compute-F", "#databricks-Compute-NCsv3", "#databricks-General-all-DSV2", "#databricks-General-all-Dv2", "#databricks-General-all-DSv3", "#databricks-Memory-all-DSv2", "#databricks-Memory-all-Dv2", "#databricks-Memory-all-Esv3", "#databricks-Compute-all-Fsv2", "#databricks-Compute-all-F", "#databricks-Compute-all-NCsv3", "#databricks-General-job-DSV2", "#databricks-General-job-Dv2", "#databricks-General-job-DSv3", "#databricks-Memory-job-DSv2", "#databricks-Memory-job-Dv2", "#databricks-Memory-job-Esv3", "#databricks-Compute-job-Fsv2", "#databricks-Compute-job-F", "#databricks-Compute-job-NCsv3"]}, {"os": "databricks", "region": "north-china", "tableIDs": ["#databricks-data-analysis", "#databricks-data-analysis1", "#databricks-data-analysis2", "#databricks-data-analysis3", "#databricks-data-analysis4", "#databricks-data-analysis5", "#databricks-data-analysis6", "#databricks-data-analysis7", "#databricks-data-analysis8", "#databricks-data-analysis9", "#databricks-data-analysis10", "#databricks-data-analysis11", "#databricks-data-analysis12", "#databricks-data-analysis13", "#databricks-data-analysis14", "#databricks-data-analysis15", "#databricks-data-analysis16", "#databricks-data-analysis17", "#databricks-data-analysis18", "#databricks-data-analysis19", "#databricks-data-analysis20", "#databricks-data-analysis21", "#databricks-data-analysis22", "#databricks-data-analysis23", "#databricks-data-analysis24", "#databricks-data-analysisN-1", "#databricks-data-analysisN-2", "#databricks-data-analysisN-3", "#databricks-General-DSV2", "#databricks-General-Dv2", "#databricks-General-DSv3", "#databricks-Memory-DSv2", "#databricks-Memory-Dv2", "#databricks-Memory-Esv3", "#databricks-Compute-Fsv2", "#databricks-Compute-F", "#databricks-Compute-NCsv3", "#databricks-General-all-DSV2", "#databricks-General-all-Dv2", "#databricks-General-all-DSv3", "#databricks-Memory-all-DSv2", "#databricks-Memory-all-Dv2", "#databricks-Memory-all-Esv3", "#databricks-Compute-all-Fsv2", "#databricks-Compute-all-F", "#databricks-Compute-all-NCsv3", "#databricks-General-job-DSV2", "#databricks-General-job-Dv2", "#databricks-General-job-DSv3", "#databricks-Memory-job-DSv2", "#databricks-Memory-job-Dv2", "#databricks-Memory-job-Esv3", "#databricks-Compute-job-Fsv2", "#databricks-Compute-job-F", "#databricks-Compute-job-NCsv3"]}]