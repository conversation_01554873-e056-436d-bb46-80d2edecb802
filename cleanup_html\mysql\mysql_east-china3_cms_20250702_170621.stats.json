{"region": {"id": "east-china3", "name": "中国东部3"}, "statistics": {"original_size": 65177, "final_size": 9369, "compression_ratio": 0.144, "filtered_tables": 4, "retained_tables": 7, "processing_time": 0.200664}, "verification": {"has_main_content": true, "has_region_info": true, "table_count": 7, "paragraph_count": 21, "heading_count": 15, "list_count": 2, "link_count": 5, "text_length": 3613, "html_size": 9369, "is_valid_html": true, "total_rows": 34, "tables_with_data": 7, "table_details": [{"id": "Azure_Database_For_MySQL5", "row_count": 10, "data_row_count": 9, "has_data": true, "sample_data": [["基本", "1", "1 GB", "￥ 0.0723/小时"], ["B1MS", "1", "2 GB", "￥ 0.1449/小时"]]}, {"id": "Azure_Database_For_MySQL7", "row_count": 8, "data_row_count": 7, "has_data": true, "sample_data": [["D2ds v4", "2", "8 GB", "￥ 1.06/小时"], ["D4ds v4", "4", "16 GB", "￥ 2.21/小时"]]}, {"id": "Azure_Database_For_MySQL8", "row_count": 8, "data_row_count": 7, "has_data": true, "sample_data": [["E2ds v4", "2", "16 GB", "￥ 1.66/小时"], ["E4ds v4", "4", "32 GB", "￥ 3.33/小时"]]}, {"id": "Azure_Database_For_MySQL20", "row_count": 2, "data_row_count": 1, "has_data": true, "sample_data": [["GB/月", "￥ 0.7653"]]}, {"id": "Azure_Database_For_MySQL1", "row_count": 2, "data_row_count": 1, "has_data": true, "sample_data": [["IOPS/月", "￥ 0.3528"]]}, {"id": "Azure_Database_For_MySQL_IOPS_East3", "row_count": 2, "data_row_count": 1, "has_data": true, "sample_data": [["付费 IO 本地冗余存储(LRS)", "每百万 IOPS ￥2.03"]]}, {"id": "Azure_Database_For_MySQL22", "row_count": 2, "data_row_count": 1, "has_data": true, "sample_data": [["GB/月", "￥ 0.5472", "由于 GRS 会创建一个副本，因此需要两倍的存储容量。如果您将备份存储配置为异地冗余存储 (GRS)，价格将是此价格的两倍。每 GB 的价格保持不变。"]]}], "content_completeness": {"has_text_content": true, "has_structured_content": true, "has_navigation_structure": true, "has_interactive_content": true}}, "extraction_info": {"source_file": "prod-html/mysql-index.html", "extracted_at": "2025-07-02T17:06:21.398678", "extraction_method": "cms_optimized", "version": "1.0_cms"}}