<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Database for MySQL 定价 - 中国北部3</title>
    <meta name="description" content="Azure Database for MySQL 在中国北部3的定价信息">
    <style>
        /* CMS友好的基础样式 */
        .product-banner {
            margin-bottom: 2rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-left: 4px solid #0078d4;
        }
        
        .product-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #0078d4;
        }
        
        .product-description {
            color: #666;
            line-height: 1.5;
        }
        
        .region-info {
            background-color: #e7f3ff;
            padding: 0.5rem 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
            color: #0078d4;
        }
        
        .pricing-content {
            margin-bottom: 2rem;
        }
        
        .table-title {
            font-size: 1.2rem;
            margin: 1.5rem 0 0.5rem 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 0.5rem;
        }
        
        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
            background-color: white;
        }
        
        .pricing-table th,
        .pricing-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        
        .pricing-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .pricing-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .faq-section {
            margin-top: 2rem;
        }
        
        .faq-title {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #0078d4;
            border-bottom: 2px solid #0078d4;
            padding-bottom: 0.5rem;
        }
        
        .faq-item {
            margin-bottom: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        .faq-question {
            background-color: #f8f9fa;
            padding: 0.75rem;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .faq-answer {
            padding: 0.75rem;
            line-height: 1.5;
            color: #666;
        }
        
        .sla-section {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #f0f8ff;
            border-radius: 4px;
        }
        
        .sla-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #0078d4;
        }
        
        .sla-content {
            margin-bottom: 0.5rem;
            color: #333;
            line-height: 1.5;
        }
    </style>
</head>
<body>
<p class="region-info">区域: 中国北部3</p><h1>AzureDatabase for MySQL</h1><p>面向应用开发人员的托管 MySQL 数据库服务</p><p>Azure Database for MySQL 通过内置功能（包括高可用性）提供用于应用开发和部署的完全托管数据库服务，无需额外付费。</p><p>通过简化的开发人员体验，灵活服务器为数据库提供最大程度的控制，最适合需要满足以下要求的工作负载:</p><ul><li>适用于数据库优化的自定义维护时段和其他配置参数</li><li>最高可达 99.99% SLA 的区域冗余和相同区域高可用性</li><li>适用于成本优化的停止/启动功能和可突发 SKU</li><li>使用数据传入复制进行混合数据同步</li><li>脱机和联机迁移支持</li></ul><h1>可突增</h1><p>具有灵活计算要求的工作负载。</p><table class="pricing-table" id="Azure_Database_For_MySQL5"><tr><th>实例</th><th>vCore 数</th><th>内存</th><th>现用现付</th></tr><tr><td>基本</td><td>1</td><td>1 GB</td><td>￥ 0.0723/小时</td></tr><tr><td>B1MS</td><td>1</td><td>2 GB</td><td>￥ 0.1449/小时</td></tr><tr><td>B2S</td><td>2</td><td>4 GB</td><td>￥ 0.5796/小时</td></tr><tr><td>B2ms</td><td>2</td><td>8 GB</td><td>￥ 1.1575/小时</td></tr><tr><td>B4ms</td><td>4</td><td>16 GB</td><td>￥ 2.315/小时</td></tr><tr><td>B8ms</td><td>8</td><td>32 GB</td><td>￥ 4.6364/小时</td></tr><tr><td>B12ms</td><td>12</td><td>48 GB</td><td>￥ 6.9515/小时</td></tr><tr><td>B16ms</td><td>16</td><td>64 GB</td><td>￥ 9.2729/小时</td></tr><tr><td>B20ms</td><td>20</td><td>80 GB</td><td>￥ 11.58792/小时</td></tr></table><h1>常规用途</h1><p>大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放 I/O 吞吐量。</p><h3>常规用途</h3><table class="pricing-table" id="Azure_Database_For_MySQL7"><tr><th>实例</th><th>vCore 数</th><th>内存</th><th>现用现付</th></tr><tr><td>D2ds v4</td><td>2</td><td>8 GB</td><td>￥ 1.06/小时</td></tr><tr><td>D4ds v4</td><td>4</td><td>16 GB</td><td>￥ 2.21/小时</td></tr><tr><td>D8ds v4</td><td>8</td><td>32 GB</td><td>￥ 4.23/小时</td></tr><tr><td>D16ds v4</td><td>16</td><td>64 GB</td><td>￥ 8.47/小时</td></tr><tr><td>D32ds v4</td><td>32</td><td>128 GB</td><td>￥ 16.96/小时</td></tr><tr><td>D48ds v4</td><td>48</td><td>192 GB</td><td>￥ 39.94/小时</td></tr><tr><td>D64ds v4</td><td>64</td><td>256 GB</td><td>￥ 53.12/小时</td></tr></table><h1>业务关键</h1><p>高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。</p><h3>业务关键</h3><table class="pricing-table" id="Azure_Database_For_MySQL8"><tr><th>实例</th><th>vCore 数</th><th>内存</th><th>现用现付</th></tr><tr><td>E2ds v4</td><td>2</td><td>16 GB</td><td>￥ 1.66/小时</td></tr><tr><td>E4ds v4</td><td>4</td><td>32 GB</td><td>￥ 3.33/小时</td></tr><tr><td>E8ds v4</td><td>8</td><td>64 GB</td><td>￥ 6.66/小时</td></tr><tr><td>E16ds v4</td><td>16</td><td>128 GB</td><td>￥ 13.31/小时</td></tr><tr><td>E32ds v4</td><td>32</td><td>256 GB</td><td>￥ 26.56/小时</td></tr><tr><td>E48ds v4</td><td>48</td><td>384 GB</td><td>￥ 39.94/小时</td></tr><tr><td>E64ds v4</td><td>64</td><td>504 GB</td><td>￥ 53.12/小时</td></tr></table><h3>存储</h3><p>需要为对服务器预配的存储空间付费。存储空间最多可预配到 16 TB，每 GB 存储的 IOPS 为 3。</p><table class="pricing-table" id="Azure_Database_For_MySQL20"><tr><th></th><th>价格</th></tr><tr><td>GB/月</td><td>￥ 0.7653</td></tr></table><h3>其他 IOPS</h3><p>可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3
                                                                    IOPS/GB)，我们将按每月每 IOPS ￥0.3528 的价格向你收费。超额 IOPS
                                                                    按每分钟计费；不足一分钟的，按一分钟计算。请详细了解<a href="https://docs.azure.cn/zh-cn/mysql/concepts-pricing-tiers">超额
                                                                        IOPS</a>。</p><table class="pricing-table" id="Azure_Database_For_MySQL1"><tr><th></th><th>价格</th></tr><tr><td>IOPS/月</td><td>￥ 0.3528</td></tr></table><h3>付费 IO</h3><p>Azure Database for MySQL 将根据工作负载自动缩放 IOPS，而无需手动预配 IOPS。这是一种经济高效的 IO 模型，仅对工作负载消耗的内容收费。</p><table class="pricing-table" id="Azure_Database_For_MySQL_IOPS"><tr><th></th><th>价格</th></tr><tr><td>付费 IO 本地冗余存储(LRS)</td><td>每百万 IOPS ￥2.03</td></tr><tr><td>付费 IO 区域冗余存储(ZRS)</td><td>每百万 IOPS ￥2.544</td></tr></table><h3>备份存储</h3><p>备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过
                                                                100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。</p><table class="pricing-table" id="Azure_Database_For_MySQL22"><tr><th></th><th>价格</th><th>备注</th></tr><tr><td>GB/月</td><td>￥ 0.5472</td><td>由于 GRS 会创建一个副本，因此需要两倍的存储容量。如果您将备份存储配置为异地冗余存储 (GRS)，价格将是此价格的两倍。每 GB 的价格保持不变。</td></tr></table><h3>只读副本</h3><p>有了只读副本，执行大量读取操作的工作负载就能超越单个 Azure Database for MySQL
                                                                灵活服务器的容量向外扩展。每个只读副本按照预配计算资源的 vCore 数量以及每月 GB 存储量计费。</p><h2>常见问题</h2><h3>MySQL Database on Azure 迁移客户常见问题解答</h3><p>我们列下了很详尽的迁移指南，请参考<a href="../../../support/announcement/mysql-migration-faq.html">MySQL Database on
                                        Azure 迁移常见问题与指南</a>。</p><h3>常规</h3><p>基本层专为需要轻型计算和 I/O
                                                    性能的工作负荷而设计。相关示例包括用于开发或测试的服务器，或者不常使用的小型应用程序。常规用途层适用于大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放
                                                    I/O 吞吐量。相关示例包括用于托管 Web
                                                    和移动应用的服务器，以及其他企业应用程序。内存优化层适用于高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。相关示例包括用于处理实时数据的服务器，以及高性能事务性应用或分析应用。请参阅<a href="https://docs.azure.cn/zh-cn/mysql/concepts-pricing-tiers/">文档</a>，了解详细信息。</p><p>MySQL 是按 Server 来计费的，与 SQL Database 相反（SQL Database 是按照数据库来收费的）</p><p>对于所有层，服务根据定价层、在 vCore 中预配的计算以及为服务器和备份预配的存储空间（GB/月）按可预测的每小时费率计费。vCore
                                                    小时数、服务器的存储空间（GB/月）和备份的存储空间（GB/月）作为单独的行项出现在订单上。</p><p>需要为 MySQL
                                                    存在的每个小时或不足一小时的部分付费，而无需考虑服务器是否在整个小时内处于活动状态。如果已缩放数据库，在这个小时内，按照使用的最高定价层、预配的
                                                    vCore 和存储空间计费。</p><p>例如：</p><ul><li>如果创建一个 MySQL 服务器并在 5 分钟后删除它，则按照预配的计算和存储空间收取一整个小时的费用。</li><li>如果在常规用途层创建一个具有 8 个 vCore 的 MySQL 服务器，并在常规用途层中立即将其升级为 16 个
                                                        vCore，则第一个小时按 16 个 vCore 收费。</li></ul><p>备份存储是与服务器的自动备份关联的存储。增长备份保留期会使 MySQL 服务器使用的备份存储空间增大。如果备份存储空间未超过 100%
                                                    的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。例如，如果数据库存储空间大小为 100
                                                    GB，则可以获得 100 GB 的备份，无需额外付费。但是，如果备份为 110 GB，则需要为 10 GB 付费。</p><h2>支持和服务级别协议</h2><p>如有任何疑问或需要帮助，请访问<a href="https://support.azure.cn/zh-cn/support/contact" id="mysql-contact-page">Azure 支持</a>选择自助服务或者其他任何方式联系我们获得支持。</p><p>我们保证，客户的 Azure Database for MySQL 服务器与我们的 Internet 网关至少可在 99.99%
                                的时间内保持连接。若要了解有关我们的服务器级别协议的详细信息，请访问<a href="../../../support/sla/mysql/index.html" id="pricing_mysql_sla">服务级别协议</a>页。</p>
</body>
</html>